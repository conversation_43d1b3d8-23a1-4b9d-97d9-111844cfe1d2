#!/bin/bash

# Test the n8n workflow after manual setup
echo "🧪 Testing n8n Targeted Code Fixes Workflow"

# Test webhook endpoint
echo "📡 Testing webhook endpoint..."
response=$(curl -s -X POST http://localhost:5678/webhook/fix-code \
  -H "Content-Type: application/json" \
  -d '{
    "taskFile": "/private/var/www/2025/ollamar1/beauty-crm/shared-product-engineering/sdlc-monster-agents/tasks/planner-ui-fixes.md"
  }')

if [[ $? -eq 0 ]]; then
  echo "✅ Webhook responded successfully"
  echo "📋 Response:"
  echo "$response" | jq '.' 2>/dev/null || echo "$response"
else
  echo "❌ Webhook test failed"
  echo "🔧 Make sure:"
  echo "   1. n8n workflow is imported"
  echo "   2. Gemini API key is configured"
  echo "   3. Workflow is activated"
fi

echo ""
echo "🎯 Next steps if successful:"
echo "   - Check logs/targeted-fixes.log for detailed execution"
echo "   - Verify backups/ directory for file changes"
echo "   - Review actual code modifications in appointment planner"
