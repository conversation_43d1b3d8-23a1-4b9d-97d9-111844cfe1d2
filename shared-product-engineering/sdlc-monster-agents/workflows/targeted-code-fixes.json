n{
  "name": "Targeted Code Fixes - LangChain + Gemini 2.5 Flash",
  "nodes": [
    {
      "parameters": {
        "httpMethod": "POST",
        "path": "fix-code",
        "responseMode": "responseNode",
        "options": {}
      },
      "id": "webhook-trigger",
      "name": "Webhook Trigger",
      "type": "n8n-nodes-base.webhook",
      "typeVersion": 2,
      "position": [240, 300],
      "webhookId": "targeted-fixes"
    },
    {
      "parameters": {
        "jsCode": "// Task Splitter - Break down large tasks into small, targeted fixes\nconst input = $input.all()[0].json;\nconst taskFile = input.body?.taskFile || '/private/var/www/2025/ollamar1/beauty-crm/shared-product-engineering/sdlc-monster-agents/tasks/planner-ui-fixes.md';\n\nconsole.log('Splitting tasks for targeted fixes...');\n\ntry {\n  const fs = require('fs');\n  const content = fs.readFileSync(taskFile, 'utf8');\n  const lines = content.split('\\n');\n  const smallTasks = [];\n  \n  for (let i = 0; i < lines.length; i++) {\n    const line = lines[i];\n    const taskMatch = line.match(/^- \\[ \\] (.+)$/);\n    \n    if (taskMatch) {\n      const description = taskMatch[1];\n      let filePath = '';\n      let codeContext = '';\n      \n      // Extract minimal context for targeted fixes\n      for (let j = i + 1; j < lines.length && j < i + 5; j++) {\n        if (lines[j].startsWith('Path:') || lines[j].startsWith('File:')) {\n          filePath = lines[j].replace(/^(Path|File):\\s*/, '').trim();\n        }\n        if (lines[j].startsWith('```')) {\n          j++;\n          let contextLines = 0;\n          while (j < lines.length && !lines[j].startsWith('```') && contextLines < 10) {\n            codeContext += lines[j] + '\\n';\n            j++;\n            contextLines++;\n          }\n          break;\n        }\n        if (lines[j].startsWith('- [')) break;\n      }\n      \n      // Create small, targeted task\n      const smallTask = {\n        id: `task-${smallTasks.length + 1}`,\n        description: description,\n        filePath: filePath,\n        codeContext: codeContext.trim(),\n        type: getTaskType(description),\n        priority: getTaskPriority(description),\n        maxContext: 500 // Limit context to prevent overwrites\n      };\n      \n      smallTasks.push(smallTask);\n    }\n  }\n  \n  console.log(`Split into ${smallTasks.length} targeted tasks`);\n  \n  return smallTasks.map(task => ({ json: task }));\n  \n} catch (error) {\n  console.error('Error splitting tasks:', error.message);\n  return [{ json: { error: error.message } }];\n}\n\n// Helper functions\nfunction getTaskType(description) {\n  if (description.includes('Router') || description.includes('router')) return 'router-fix';\n  if (description.includes('CSS') || description.includes('style')) return 'css-fix';\n  if (description.includes('TypeScript') || description.includes('interface')) return 'type-fix';\n  if (description.includes('mobile') || description.includes('responsive')) return 'mobile-fix';\n  if (description.includes('ARIA') || description.includes('accessibility')) return 'a11y-fix';\n  if (description.includes('performance') || description.includes('optimization')) return 'perf-fix';\n  return 'general-fix';\n}\n\nfunction getTaskPriority(description) {\n  if (description.includes('error') || description.includes('fix') || description.includes('duplicate')) return 'high';\n  if (description.includes('improve') || description.includes('enhance')) return 'medium';\n  return 'low';\n}"
      },
      "id": "task-splitter",
      "name": "Task Splitter",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [460, 300]
    },
    {
      "parameters": {
        "model": "gemini-2.0-flash-exp",
        "options": {
          "temperature": 0.1,
          "maxTokens": 1000
        }
      },
      "id": "gemini-model",
      "name": "Gemini 2.5 Flash",
      "type": "n8n-nodes-langchain.lmchatgooglegemini",
      "typeVersion": 1,
      "position": [680, 200]
    },
    {
      "parameters": {
        "jsCode": "// LangChain Code Node - Targeted Code Generation\n// Use small context and specific prompts to avoid overwrites\n\nconst task = $input.all()[0].json;\n\nconsole.log(`Processing targeted task: ${task.description}`);\n\n// Create targeted prompt based on task type\nconst prompt = createTargetedPrompt(task);\n\n// Use Gemini with small context\nconst model = this.getInputConnectionData('ai_languageModel', 0);\n\ntry {\n  const response = await model.invoke(prompt);\n  \n  const result = {\n    taskId: task.id,\n    description: task.description,\n    filePath: task.filePath,\n    type: task.type,\n    priority: task.priority,\n    solution: response.content,\n    confidence: calculateConfidence(task, response),\n    isTargeted: true,\n    contextSize: prompt.length\n  };\n  \n  console.log(`Generated targeted solution for ${task.type} with confidence ${result.confidence}`);\n  \n  return [{ json: result }];\n  \n} catch (error) {\n  console.error('Error generating solution:', error.message);\n  return [{ json: { error: error.message, task: task } }];\n}\n\n// Helper functions\nfunction createTargetedPrompt(task) {\n  const basePrompt = `You are a senior React/TypeScript developer. Make a SMALL, TARGETED fix for this specific issue.\n\nIMPORTANT RULES:\n- Only fix the specific issue mentioned\n- Do NOT rewrite entire files or components\n- Preserve ALL existing logic and functionality\n- Provide minimal, surgical changes only\n- Include line numbers for exact placement\n- Maximum 20 lines of code changes\n\nTask: ${task.description}\nFile: ${task.filePath}\nTask Type: ${task.type}\n`;\n\n  if (task.codeContext) {\n    return basePrompt + `\\n\\nExisting Code Context (DO NOT REPLACE):\\n\\`\\`\\`\\n${task.codeContext.substring(0, 300)}\\n\\`\\`\\`\\n\\nProvide ONLY the specific lines that need to be changed:`;\n  }\n  \n  return basePrompt + '\\n\\nProvide the minimal code change needed:';\n}\n\nfunction calculateConfidence(task, response) {\n  let confidence = 0.7; // Base confidence\n  \n  // Higher confidence for specific task types\n  if (task.type === 'css-fix' || task.type === 'type-fix') confidence += 0.1;\n  if (task.priority === 'high') confidence += 0.1;\n  if (response.content.length < 500) confidence += 0.1; // Smaller changes are safer\n  if (response.content.includes('// TARGETED FIX')) confidence += 0.05;\n  \n  return Math.min(confidence, 0.95);\n}"
      },
      "id": "langchain-code",
      "name": "LangChain Code - Targeted Fix",
      "type": "n8n-nodes-langchain.code",
      "typeVersion": 1,
      "position": [680, 300]
    },
    {
      "parameters": {
        "conditions": {
          "options": {
            "caseSensitive": true,
            "leftValue": "={{ $json.confidence }}",
            "operation": "largerEqual",
            "rightValue": 0.85
          }
        }
      },
      "id": "confidence-filter",
      "name": "High Confidence Filter",
      "type": "n8n-nodes-base.if",
      "typeVersion": 2,
      "position": [900, 300]
    },
    {
      "parameters": {
        "jsCode": "// Safe File Modifier - Apply only high-confidence, targeted changes\nconst solution = $input.all()[0].json;\n\nconsole.log(`Applying safe modification for: ${solution.description}`);\n\ntry {\n  const fs = require('fs');\n  const path = require('path');\n  \n  // Validate file path\n  const fullPath = path.resolve(solution.filePath);\n  if (!fs.existsSync(fullPath)) {\n    console.log(`File not found: ${fullPath}`);\n    return [{ json: { ...solution, status: 'file-not-found', applied: false } }];\n  }\n  \n  // Create backup\n  const backupPath = `${fullPath}.backup.${Date.now()}`;\n  fs.copyFileSync(fullPath, backupPath);\n  console.log(`Backup created: ${backupPath}`);\n  \n  // Read current content\n  const currentContent = fs.readFileSync(fullPath, 'utf8');\n  \n  // Apply targeted change (this would need specific implementation based on solution type)\n  const modifiedContent = applyTargetedChange(currentContent, solution);\n  \n  if (modifiedContent !== currentContent) {\n    fs.writeFileSync(fullPath, modifiedContent);\n    console.log(`Applied targeted fix to ${solution.filePath}`);\n    \n    return [{ json: {\n      ...solution,\n      status: 'applied',\n      applied: true,\n      backupPath: backupPath,\n      linesChanged: countChangedLines(currentContent, modifiedContent)\n    } }];\n  } else {\n    return [{ json: { ...solution, status: 'no-changes-needed', applied: false } }];\n  }\n  \n} catch (error) {\n  console.error('Error applying modification:', error.message);\n  return [{ json: { ...solution, error: error.message, applied: false } }];\n}\n\n// Helper functions\nfunction applyTargetedChange(content, solution) {\n  // This is a simplified example - real implementation would parse the solution\n  // and apply specific line changes based on the solution type\n  \n  if (solution.type === 'router-fix' && content.includes('<Router>')) {\n    // Example: Replace nested Router with useRoutes\n    return content.replace(/<Router>.*?<\\/Router>/s, solution.solution);\n  }\n  \n  if (solution.type === 'css-fix' && solution.filePath.endsWith('.css')) {\n    // Example: Append CSS rules\n    return content + '\\n\\n' + solution.solution;\n  }\n  \n  // For other types, return original content to prevent overwrites\n  console.log(`Safe mode: No automatic application for ${solution.type}`);\n  return content;\n}\n\nfunction countChangedLines(original, modified) {\n  const originalLines = original.split('\\n');\n  const modifiedLines = modified.split('\\n');\n  return Math.abs(originalLines.length - modifiedLines.length);\n}"
      },
      "id": "safe-modifier",
      "name": "Safe File Modifier",
      "type": "n8n-nodes-base.code",
      "typeVersion": 2,
      "position": [1120, 240]
    },
    {
      "parameters": {
        "respondWith": "json",
        "responseBody": "={{ JSON.stringify($json, null, 2) }}"
      },
      "id": "response",
      "name": "Response",
      "type": "n8n-nodes-base.respondToWebhook",
      "typeVersion": 1,
      "position": [1340, 300]
    }
  ],
  "connections": {
    "Webhook Trigger": {
      "main": [
        [
          {
            "node": "Task Splitter",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Task Splitter": {
      "main": [
        [
          {
            "node": "LangChain Code - Targeted Fix",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Gemini 2.5 Flash": {
      "ai_languageModel": [
        [
          {
            "node": "LangChain Code - Targeted Fix",
            "type": "ai_languageModel",
            "index": 0
          }
        ]
      ]
    },
    "LangChain Code - Targeted Fix": {
      "main": [
        [
          {
            "node": "High Confidence Filter",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "High Confidence Filter": {
      "main": [
        [
          {
            "node": "Safe File Modifier",
            "type": "main",
            "index": 0
          }
        ],
        [
          {
            "node": "Response",
            "type": "main",
            "index": 0
          }
        ]
      ]
    },
    "Safe File Modifier": {
      "main": [
        [
          {
            "node": "Response",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  },
  "settings": {
    "executionOrder": "v1"
  }
}
